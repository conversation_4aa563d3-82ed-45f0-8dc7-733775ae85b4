export const archiveServiceData = {
  name: "Archive Service",
  description: "Data archival and retrieval service for assessment results, job tracking, and statistical analysis. Provides comprehensive access to historical assessment data and analytics.",
  baseUrl: "https://api.chhrone.web.id/api/archive",
  version: "1.0.0",
  port: "3002",
  endpoints: [
    {
      method: "GET",
      path: "/api/archive/results",
      title: "Get User Results",
      description: "Retrieve all assessment results for the authenticated user with optional filtering and pagination.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "page",
          type: "integer",
          required: false,
          description: "Page number for pagination (default: 1)"
        },
        {
          name: "limit",
          type: "integer",
          required: false,
          description: "Number of results per page (default: 10, max: 100)"
        },
        {
          name: "sortBy",
          type: "string",
          required: false,
          description: "Sort field: 'created_at', 'assessment_name', 'status' (default: 'created_at')"
        },
        {
          name: "sortOrder",
          type: "string",
          required: false,
          description: "Sort order: 'asc' or 'desc' (default: 'desc')"
        },
        {
          name: "status",
          type: "string",
          required: false,
          description: "Filter by status: 'completed', 'failed', 'processing'"
        },
        {
          name: "assessmentName",
          type: "string",
          required: false,
          description: "Filter by assessment name"
        }
      ],
      response: {
        success: true,
        data: {
          results: [
            {
              id: "550e8400-e29b-41d4-a716-************",
              userId: "550e8400-e29b-41d4-a716-************",
              jobId: "550e8400-e29b-41d4-a716-************",
              assessmentName: "AI-Driven Talent Mapping",
              status: "completed",
              createdAt: "2024-01-01T10:00:00Z",
              completedAt: "2024-01-01T10:05:00Z",
              processingTime: 300,
              aiAnalysis: {
                personalityProfile: "ENFP - The Campaigner",
                careerRecommendations: ["Software Developer", "Marketing Manager"],
                strengthsAnalysis: "High creativity and curiosity drive innovation",
                developmentAreas: "Focus on improving self-regulation skills"
              },
              scores: {
                riasec: {
                  realistic: 75,
                  investigative: 80,
                  artistic: 65,
                  social: 70,
                  enterprising: 85,
                  conventional: 60
                },
                ocean: {
                  openness: 80,
                  conscientiousness: 75,
                  extraversion: 70,
                  agreeableness: 85,
                  neuroticism: 40
                }
              }
            }
          ],
          pagination: {
            currentPage: 1,
            totalPages: 5,
            totalResults: 50,
            hasNextPage: true,
            hasPreviousPage: false
          }
        }
      },
      example: `curl -X GET "https://api.chhrone.web.id/api/archive/results?page=1&limit=10&sortBy=created_at&sortOrder=desc" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "GET",
      path: "/api/archive/results/:resultId",
      title: "Get Specific Result",
      description: "Retrieve detailed information about a specific assessment result by ID.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "resultId",
          type: "string",
          required: true,
          description: "UUID of the assessment result"
        }
      ],
      response: {
        success: true,
        data: {
          result: {
            id: "550e8400-e29b-41d4-a716-************",
            userId: "550e8400-e29b-41d4-a716-************",
            jobId: "550e8400-e29b-41d4-a716-************",
            assessmentName: "AI-Driven Talent Mapping",
            status: "completed",
            createdAt: "2024-01-01T10:00:00Z",
            completedAt: "2024-01-01T10:05:00Z",
            processingTime: 300,
            aiAnalysis: {
              personalityProfile: "ENFP - The Campaigner",
              careerRecommendations: ["Software Developer", "Marketing Manager", "UX Designer"],
              strengthsAnalysis: "High creativity and curiosity drive innovation and problem-solving",
              developmentAreas: "Focus on improving self-regulation and conventional skills",
              detailedInsights: {
                riasecAnalysis: "Strong enterprising and investigative traits suggest leadership in tech",
                oceanAnalysis: "High openness and agreeableness indicate collaborative innovation style",
                viaIsAnalysis: "Top strengths in curiosity, creativity, and love of learning"
              }
            },
            scores: {
              riasec: {
                realistic: 75,
                investigative: 80,
                artistic: 65,
                social: 70,
                enterprising: 85,
                conventional: 60
              },
              ocean: {
                openness: 80,
                conscientiousness: 75,
                extraversion: 70,
                agreeableness: 85,
                neuroticism: 40
              },
              viaIs: {
                creativity: 80,
                curiosity: 85,
                judgment: 75,
                loveOfLearning: 90,
                perspective: 70
              }
            }
          }
        }
      },
      example: `curl -X GET https://api.chhrone.web.id/api/archive/results/550e8400-e29b-41d4-a716-************ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "GET",
      path: "/api/archive/jobs",
      title: "Get User Jobs",
      description: "Retrieve all assessment jobs for the authenticated user with status tracking.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "page",
          type: "integer",
          required: false,
          description: "Page number for pagination (default: 1)"
        },
        {
          name: "limit",
          type: "integer",
          required: false,
          description: "Number of jobs per page (default: 10, max: 100)"
        },
        {
          name: "status",
          type: "string",
          required: false,
          description: "Filter by status: 'queued', 'processing', 'completed', 'failed'"
        }
      ],
      response: {
        success: true,
        data: {
          jobs: [
            {
              id: "550e8400-e29b-41d4-a716-************",
              userId: "550e8400-e29b-41d4-a716-************",
              status: "completed",
              progress: 100,
              createdAt: "2024-01-01T10:00:00Z",
              updatedAt: "2024-01-01T10:05:00Z",
              completedAt: "2024-01-01T10:05:00Z",
              processingTime: 300,
              resultId: "550e8400-e29b-41d4-a716-************",
              assessmentName: "AI-Driven Talent Mapping",
              error: null
            }
          ],
          pagination: {
            currentPage: 1,
            totalPages: 3,
            totalJobs: 25,
            hasNextPage: true,
            hasPreviousPage: false
          }
        }
      },
      example: `curl -X GET "https://api.chhrone.web.id/api/archive/jobs?status=completed&page=1&limit=10" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "GET",
      path: "/api/archive/jobs/:jobId",
      title: "Get Specific Job",
      description: "Retrieve detailed information about a specific assessment job by ID.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "jobId",
          type: "string",
          required: true,
          description: "UUID of the assessment job"
        }
      ],
      response: {
        success: true,
        data: {
          job: {
            id: "550e8400-e29b-41d4-a716-************",
            userId: "550e8400-e29b-41d4-a716-************",
            status: "completed",
            progress: 100,
            createdAt: "2024-01-01T10:00:00Z",
            updatedAt: "2024-01-01T10:05:00Z",
            completedAt: "2024-01-01T10:05:00Z",
            processingTime: 300,
            resultId: "550e8400-e29b-41d4-a716-************",
            assessmentName: "AI-Driven Talent Mapping",
            queuePosition: null,
            estimatedTimeRemaining: null,
            error: null,
            metadata: {
              submissionSource: "web",
              ipAddress: "***********",
              userAgent: "Mozilla/5.0..."
            }
          }
        }
      },
      example: `curl -X GET https://api.chhrone.web.id/api/archive/jobs/550e8400-e29b-41d4-a716-************ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "GET",
      path: "/api/archive/statistics",
      title: "Get User Statistics",
      description: "Get comprehensive statistics about the user's assessment history and performance trends.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      response: {
        success: true,
        data: {
          totalAssessments: 15,
          completedAssessments: 12,
          failedAssessments: 1,
          processingAssessments: 2,
          averageProcessingTime: 285,
          assessmentTypes: {
            "AI-Driven Talent Mapping": 10,
            "AI-Based IQ Test": 3,
            "Custom Assessment": 2
          },
          monthlyTrends: [
            {
              month: "2024-01",
              assessments: 5,
              averageScore: 78.5
            },
            {
              month: "2024-02",
              assessments: 7,
              averageScore: 82.1
            }
          ],
          personalityTrends: {
            riasec: {
              realistic: { current: 75, trend: "+5%" },
              investigative: { current: 80, trend: "+2%" },
              artistic: { current: 65, trend: "-1%" },
              social: { current: 70, trend: "+3%" },
              enterprising: { current: 85, trend: "+7%" },
              conventional: { current: 60, trend: "0%" }
            },
            ocean: {
              openness: { current: 80, trend: "+4%" },
              conscientiousness: { current: 75, trend: "+1%" },
              extraversion: { current: 70, trend: "+2%" },
              agreeableness: { current: 85, trend: "+3%" },
              neuroticism: { current: 40, trend: "-5%" }
            }
          }
        }
      },
      example: `curl -X GET https://api.chhrone.web.id/api/archive/statistics \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "DELETE",
      path: "/api/archive/results/:resultId",
      title: "Delete Result",
      description: "Delete a specific assessment result. This action cannot be undone.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "resultId",
          type: "string",
          required: true,
          description: "UUID of the assessment result to delete"
        }
      ],
      response: {
        success: true,
        message: "Assessment result deleted successfully",
        data: {
          deletedResultId: "550e8400-e29b-41d4-a716-************",
          deletedAt: "2024-01-15T10:30:00.000Z"
        }
      },
      example: `curl -X DELETE https://api.chhrone.web.id/api/archive/results/550e8400-e29b-41d4-a716-************ \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    },
    {
      method: "GET",
      path: "/api/archive/export",
      title: "Export User Data",
      description: "Export all user assessment data in JSON format for backup or analysis purposes.",
      authentication: "Bearer Token Required",
      rateLimit: "5000 requests per 15 minutes",
      parameters: [
        {
          name: "format",
          type: "string",
          required: false,
          description: "Export format: 'json' or 'csv' (default: 'json')"
        },
        {
          name: "includeScores",
          type: "boolean",
          required: false,
          description: "Include detailed scores in export (default: true)"
        },
        {
          name: "includeAnalysis",
          type: "boolean",
          required: false,
          description: "Include AI analysis in export (default: true)"
        }
      ],
      response: {
        success: true,
        data: {
          exportId: "550e8400-e29b-41d4-a716-************",
          downloadUrl: "https://api.chhrone.web.id/api/archive/download/550e8400-e29b-41d4-a716-************",
          expiresAt: "2024-01-15T10:30:00.000Z",
          format: "json",
          totalRecords: 15,
          fileSize: "2.5MB"
        }
      },
      example: `curl -X GET "https://api.chhrone.web.id/api/archive/export?format=json&includeScores=true" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`
    }
  ]
};
