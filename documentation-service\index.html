<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATMA API Documentation</title>
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="stylesheet" href="./src/styles/main.css">
    <link rel="stylesheet" href="./src/styles/prism.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1>ATMA API</h1>
                        <span class="version">v1.0.0</span>
                    </div>
                    <div class="base-url">
                        <span class="label">Base URL:</span>
                        <code class="url">https://api.chhrone.web.id</code>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="nav-content">
                <div class="nav-section">
                    <h3>Overview</h3>
                    <ul>
                        <li><a href="#introduction" class="nav-link">Introduction</a></li>
                        <li><a href="#authentication" class="nav-link">Authentication</a></li>
                        <li><a href="#rate-limiting" class="nav-link">Rate Limiting</a></li>
                        <li><a href="#error-handling" class="nav-link">Error Handling</a></li>
                    </ul>
                </div>
                
                <div class="nav-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="#auth-service" class="nav-link">Auth Service</a></li>
                        <li><a href="#assessment-service" class="nav-link">Assessment Service</a></li>
                        <li><a href="#archive-service" class="nav-link">Archive Service</a></li>
                        <li><a href="#chatbot-service" class="nav-link">Chatbot Service</a></li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#getting-started" class="nav-link">Getting Started</a></li>
                        <li><a href="#examples" class="nav-link">Examples</a></li>
                        <li><a href="#sdks" class="nav-link">SDKs</a></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- Introduction Section -->
                <section id="introduction" class="section">
                    <h2>Introduction</h2>
                    <p>Welcome to the ATMA (AI-Driven Talent Mapping Assessment) API documentation. This comprehensive API ecosystem provides powerful tools for talent assessment, career guidance, and AI-driven personality analysis.</p>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h3>🔐 Authentication</h3>
                            <p>Secure JWT-based authentication with role-based access control</p>
                        </div>
                        <div class="feature-card">
                            <h3>🎯 Assessment</h3>
                            <p>AI-powered personality and career assessment using RIASEC, OCEAN, and VIA-IS</p>
                        </div>
                        <div class="feature-card">
                            <h3>📊 Analytics</h3>
                            <p>Comprehensive data archival and statistical analysis</p>
                        </div>
                        <div class="feature-card">
                            <h3>💬 Chatbot</h3>
                            <p>Intelligent conversational AI for career guidance and support</p>
                        </div>
                    </div>
                </section>

                <!-- Authentication Section -->
                <section id="authentication" class="section">
                    <h2>Authentication</h2>
                    <p>All API endpoints require authentication using JWT tokens obtained from the Auth Service.</p>
                    
                    <div class="code-block">
                        <h4>Required Header</h4>
                        <pre><code class="language-http">Authorization: Bearer &lt;jwt_token&gt;</code></pre>
                    </div>

                    <div class="auth-flow">
                        <h4>Authentication Flow</h4>
                        <ol>
                            <li>Register or login to obtain JWT token</li>
                            <li>Include token in Authorization header for all requests</li>
                            <li>Token expires after 24 hours (configurable)</li>
                            <li>Refresh token when needed</li>
                        </ol>
                    </div>
                </section>

                <!-- Rate Limiting Section -->
                <section id="rate-limiting" class="section">
                    <h2>Rate Limiting</h2>
                    <p>API endpoints are protected by rate limiting to ensure fair usage and system stability.</p>
                    
                    <div class="rate-limits">
                        <div class="rate-limit-card">
                            <h4>Auth Endpoints</h4>
                            <p>100 requests per 15 minutes</p>
                        </div>
                        <div class="rate-limit-card">
                            <h4>Assessment Endpoints</h4>
                            <p>100 requests per 15 minutes</p>
                        </div>
                        <div class="rate-limit-card">
                            <h4>Archive Endpoints</h4>
                            <p>5000 requests per 15 minutes</p>
                        </div>
                        <div class="rate-limit-card">
                            <h4>Chatbot Endpoints</h4>
                            <p>50 requests per 5 minutes</p>
                        </div>
                    </div>
                </section>

                <!-- Error Handling Section -->
                <section id="error-handling" class="section">
                    <h2>Error Handling</h2>
                    <p>All API responses follow a consistent error format for easy handling.</p>
                    
                    <div class="code-block">
                        <h4>Standard Error Response</h4>
                        <pre><code class="language-json">{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "timestamp": "2024-01-15T10:30:00.000Z",
      "request_id": "req_123456789"
    }
  }
}</code></pre>
                    </div>

                    <div class="error-codes">
                        <h4>Common Error Codes</h4>
                        <table class="error-table">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>HTTP Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>UNAUTHORIZED</code></td>
                                    <td>401</td>
                                    <td>Missing or invalid authentication</td>
                                </tr>
                                <tr>
                                    <td><code>FORBIDDEN</code></td>
                                    <td>403</td>
                                    <td>Insufficient permissions</td>
                                </tr>
                                <tr>
                                    <td><code>NOT_FOUND</code></td>
                                    <td>404</td>
                                    <td>Resource not found</td>
                                </tr>
                                <tr>
                                    <td><code>VALIDATION_ERROR</code></td>
                                    <td>400</td>
                                    <td>Request validation failed</td>
                                </tr>
                                <tr>
                                    <td><code>RATE_LIMIT_EXCEEDED</code></td>
                                    <td>429</td>
                                    <td>Too many requests</td>
                                </tr>
                                <tr>
                                    <td><code>INTERNAL_ERROR</code></td>
                                    <td>500</td>
                                    <td>Internal server error</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Service Sections will be loaded dynamically -->
                <div id="services-content"></div>

                <!-- Getting Started Section -->
                <section id="getting-started" class="section">
                    <h2>Getting Started</h2>
                    <p>Follow these steps to start using the ATMA API:</p>
                    
                    <div class="steps">
                        <div class="step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>Register Account</h4>
                                <p>Create a new user account using the Auth Service registration endpoint.</p>
                                <div class="code-block">
                                    <pre><code class="language-bash">curl -X POST https://api.chhrone.web.id/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "myPassword1"
  }'</code></pre>
                                </div>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Obtain JWT Token</h4>
                                <p>Login to get your authentication token.</p>
                                <div class="code-block">
                                    <pre><code class="language-bash">curl -X POST https://api.chhrone.web.id/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "myPassword1"
  }'</code></pre>
                                </div>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>Submit Assessment</h4>
                                <p>Submit your first assessment for AI analysis.</p>
                                <div class="code-block">
                                    <pre><code class="language-bash">curl -X POST https://api.chhrone.web.id/api/assessment/submit \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d @assessment_data.json</code></pre>
                                </div>
                            </div>
                        </div>

                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>Get Results</h4>
                                <p>Retrieve your assessment results from the Archive Service.</p>
                                <div class="code-block">
                                    <pre><code class="language-bash">curl -X GET https://api.chhrone.web.id/api/archive/results \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Examples Section -->
                <section id="examples" class="section">
                    <h2>Examples</h2>
                    <p>Common usage examples and code snippets.</p>
                    
                    <div class="example-tabs">
                        <div class="tab-buttons">
                            <button class="tab-button active" data-tab="javascript">JavaScript</button>
                            <button class="tab-button" data-tab="python">Python</button>
                            <button class="tab-button" data-tab="curl">cURL</button>
                        </div>
                        
                        <div class="tab-content">
                            <div id="javascript" class="tab-pane active">
                                <div class="code-block">
                                    <h4>Complete Assessment Flow</h4>
                                    <pre><code class="language-javascript">// ATMA API Client Example
class ATMAClient {
  constructor(baseURL = 'https://api.chhrone.web.id') {
    this.baseURL = baseURL;
    this.token = null;
  }

  async login(email, password) {
    const response = await fetch(`${this.baseURL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    const data = await response.json();
    if (data.success) {
      this.token = data.data.token;
    }
    return data;
  }

  async submitAssessment(assessmentData) {
    const response = await fetch(`${this.baseURL}/api/assessment/submit`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assessmentData)
    });
    
    return await response.json();
  }

  async getResults() {
    const response = await fetch(`${this.baseURL}/api/archive/results`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });
    
    return await response.json();
  }
}

// Usage
const client = new ATMAClient();
await client.login('<EMAIL>', 'password');
const result = await client.submitAssessment(assessmentData);
console.log('Assessment submitted:', result);</code></pre>
                                </div>
                            </div>
                            
                            <div id="python" class="tab-pane">
                                <div class="code-block">
                                    <h4>Python SDK Example</h4>
                                    <pre><code class="language-python">import requests
import json

class ATMAClient:
    def __init__(self, base_url='https://api.chhrone.web.id'):
        self.base_url = base_url
        self.token = None
    
    def login(self, email, password):
        response = requests.post(
            f'{self.base_url}/api/auth/login',
            json={'email': email, 'password': password}
        )
        
        data = response.json()
        if data.get('success'):
            self.token = data['data']['token']
        return data
    
    def submit_assessment(self, assessment_data):
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(
            f'{self.base_url}/api/assessment/submit',
            headers=headers,
            json=assessment_data
        )
        
        return response.json()
    
    def get_results(self):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(
            f'{self.base_url}/api/archive/results',
            headers=headers
        )
        
        return response.json()

# Usage
client = ATMAClient()
client.login('<EMAIL>', 'password')
result = client.submit_assessment(assessment_data)
print('Assessment submitted:', result)</code></pre>
                                </div>
                            </div>
                            
                            <div id="curl" class="tab-pane">
                                <div class="code-block">
                                    <h4>cURL Examples</h4>
                                    <pre><code class="language-bash"># Login and get token
curl -X POST https://api.chhrone.web.id/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "myPassword1"
  }'

# Submit assessment
curl -X POST https://api.chhrone.web.id/api/assessment/submit \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "assessmentName": "AI-Driven Talent Mapping",
    "riasec": {
      "realistic": 75,
      "investigative": 80,
      "artistic": 65,
      "social": 70,
      "enterprising": 85,
      "conventional": 60
    },
    "ocean": {
      "openness": 80,
      "conscientiousness": 75,
      "extraversion": 70,
      "agreeableness": 85,
      "neuroticism": 40
    },
    "viaIs": {
      "creativity": 80,
      "curiosity": 85,
      "judgment": 75
    }
  }'

# Get assessment results
curl -X GET https://api.chhrone.web.id/api/archive/results \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Start chatbot conversation
curl -X POST https://api.chhrone.web.id/api/chatbot/conversations \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Career Guidance",
    "context": "assessment"
  }'</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- SDKs Section -->
                <section id="sdks" class="section">
                    <h2>SDKs & Libraries</h2>
                    <p>Official and community SDKs for different programming languages.</p>
                    
                    <div class="sdk-grid">
                        <div class="sdk-card">
                            <h4>JavaScript/TypeScript</h4>
                            <p>Official SDK for web and Node.js applications</p>
                            <div class="code-block">
                                <pre><code class="language-bash">npm install @atma/api-client</code></pre>
                            </div>
                        </div>
                        
                        <div class="sdk-card">
                            <h4>Python</h4>
                            <p>Python SDK for data science and backend applications</p>
                            <div class="code-block">
                                <pre><code class="language-bash">pip install atma-api-client</code></pre>
                            </div>
                        </div>
                        
                        <div class="sdk-card">
                            <h4>PHP</h4>
                            <p>PHP SDK for web applications</p>
                            <div class="code-block">
                                <pre><code class="language-bash">composer require atma/api-client</code></pre>
                            </div>
                        </div>
                        
                        <div class="sdk-card">
                            <h4>Go</h4>
                            <p>Go SDK for high-performance applications</p>
                            <div class="code-block">
                                <pre><code class="language-bash">go get github.com/atma/go-client</code></pre>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script type="module" src="./src/main.js"></script>
</body>
</html>
